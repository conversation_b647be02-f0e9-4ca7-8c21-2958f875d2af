/**
 * @jest-environment node
 */

import authenticationMiddleware from '../src/middlewares/authentication'
import { adminMock, memberMock, mockAuthentication } from '../test-common'
import { findChannel } from '@memberup/shared/src/libs/prisma/channel'
import handler from '@/memberup/pages/api/feed/index'

jest.mock('../src/middlewares/authentication')

jest.mock('@memberup/shared/src/libs/prisma/channel', () => ({
  findChannel: jest.fn(),
}))

jest.mock('@memberup/shared/src/libs/prisma/feed', () => ({
  createFeed: jest.fn(),
  findFeedByPermalink: jest.fn(),
  findFeeds: jest.fn(),
}))

jest.mock('@mux/mux-node')

describe('/api/feed', () => {
  let res
  beforeEach(() => {
    findChannel.mockReset()
    res = {
      status: jest.fn(() => res),
      json: jest.fn(),
    }
  })

  it.skip('POST should return 400 when not providing a valid space', async () => {
    mockAuthentication(authenticationMiddleware, memberMock)
    findChannel.mockResolvedValue(null)

    const payload = {
      title: 'Test',
      text: '<p>Test</p>',
      attachments: [],
      mentioned_users: [],
      channel_id: '60d29d24-8057-4f7b-a83d-2832a4900d2e',
      feed_type: 'default',
    }
    const request = {
      method: 'POST',
      url: '/api/feed',
      body: payload,
      query: {},
    }

    await handler(request, res)

    expect(res.status).toHaveBeenCalledWith(400)
    expect(res.json).toHaveBeenCalledWith({
      message: 'Invalid space provided.',
    })
    expect(findChannel).toHaveBeenCalledWith(payload.channel_id, memberMock.current_membership_id)
  })

  it.skip('POST should return 403 when trying to post in a private space as a member', async () => {
    mockAuthentication(authenticationMiddleware, memberMock)
    findChannel.mockResolvedValue({ id: '60d29d24-8057-4f7b-a83d-2832a4900d2e', is_private: true })

    const payload = {
      title: 'Test',
      text: '<p>Test</p>',
      attachments: [],
      mentioned_users: [],
      channel_id: '60d29d24-8057-4f7b-a83d-2832a4900d2e',
      feed_type: 'default',
    }
    const request = {
      method: 'POST',
      url: '/api/feed',
      body: payload,
      query: {},
    }

    await handler(request, res)

    expect(res.json).toHaveBeenCalledWith({
      message: 'You are not allowed to post on private spaces.',
    })
    expect(res.status).toHaveBeenCalledWith(403)

    expect(findChannel).toHaveBeenCalledWith(payload.channel_id, memberMock.current_membership_id)
  })

  it.skip('POST should return 400 when not specifying a parent_id for comments and replies', async () => {
    mockAuthentication(authenticationMiddleware, memberMock)
    findChannel.mockResolvedValue({ id: '60d29d24-8057-4f7b-a83d-2832a4900d2e', is_private: true })

    const payload = {
      title: 'Test',
      text: '<p>Test</p>',
      attachments: [],
      mentioned_users: [],
      channel_id: '60d29d24-8057-4f7b-a83d-2832a4900d2e',
      feed_type: 'comment',
    }
    const request = {
      method: 'POST',
      url: '/api/feed',
      body: payload,
      query: {},
    }

    await handler(request, res)

    expect(res.json).toHaveBeenCalledWith({
      message: 'You must specify the parent_id for comments and replies.',
    })
    expect(res.status).toHaveBeenCalledWith(400)
  })

  it.skip('POST should return 400 when not specifying a reply_parent_id for comments and replies', async () => {
    mockAuthentication(authenticationMiddleware, memberMock)
    findChannel.mockResolvedValue({ id: '60d29d24-8057-4f7b-a83d-2832a4900d2e', is_private: true })

    const payload = {
      title: 'Test',
      text: '<p>Test</p>',
      attachments: [],
      mentioned_users: [],
      channel_id: '60d29d24-8057-4f7b-a83d-2832a4900d2e',
      feed_type: 'comment',
    }
    const request = {
      method: 'POST',
      url: '/api/feed',
      body: payload,
      query: {},
    }

    await handler(request, res)

    expect(res.json).toHaveBeenCalledWith({
      message: 'You must specify the parent_id for comments and replies.',
    })
    expect(res.status).toHaveBeenCalledWith(400)
  })
})
