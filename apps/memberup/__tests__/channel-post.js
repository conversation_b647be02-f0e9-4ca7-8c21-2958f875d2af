/**
 * @jest-environment node
 */

import handler from '../pages/api/channel'
import authenticationMiddleware from '../src/middlewares/authentication'
import { adminMock, memberMock, mockAuthentication } from '../test-common'
import {
  createChannel,
  findChannels,
  hasCommunityReachedSpaceLimit,
  SpaceDuplicateException,
} from '@memberup/shared/src/libs/prisma/channel'

jest.mock('../src/middlewares/authentication')
jest.mock('@memberup/shared/src/libs/prisma/channel')

describe('/api/channel', () => {
  let req, res

  beforeEach(() => {
    // Reset the mock and set up mock request and response objects
    authenticationMiddleware.mockReset()
    createChannel.mockReset()
    findChannels.mockReset()
    hasCommunityReachedSpaceLimit.mockReset()

    //req = { headers: {}, url: '/api/feed/simple', method: 'GET' };
    res = {
      status: jest.fn(() => res),
      send: jest.fn(() => res),
      end: jest.fn(() => res),
      json: jest.fn(() => res),
    }
  })

  it('POST should return 403 if user is not an admin or creator', async () => {
    mockAuthentication(authenticationMiddleware, memberMock)

    const request = {
      method: 'POST',
      url: '/api/channel', // NOTE: If we test a handler we must provide the correct url for NextConnect
      body: {
        name: 'my-channel',
        description: 'Some channel',
        visibility: true,
        is_private: false,
      },
      query: {},
    }
    await handler(request, res)

    expect(res.json).toHaveBeenCalledWith({
      message: "You don't have the permission.",
    })
    expect(res.status).toHaveBeenCalledWith(403)
  })

  it('POST should return 400 if name is not specified during create', async () => {
    mockAuthentication(authenticationMiddleware, adminMock)
    const request = {
      method: 'POST',
      url: '/api/channel',
      body: {
        name: '',
        description: 'Some description',
        visibility: false,
        is_private: true,
      },
    }

    await handler(request, res)

    expect(res.status).toHaveBeenCalledWith(400)
    expect(res.json).toHaveBeenCalledWith({
      message: 'Space name is required.',
    })
  })

  it('POST should return 409 if channel with same name already exists for the community', async () => {
    mockAuthentication(authenticationMiddleware, adminMock)
    // Simulate a database unique constraint exception
    createChannel.mockImplementation((payload) => {
      throw new SpaceDuplicateException('my-channel')
    })
    findChannels.mockImplementation(() => {
      return {
        docs: [
          {
            name: 'my-channel',
            description: 'Some description',
            visibility: false,
            is_private: true,
          },
        ],
      }
    })
    hasCommunityReachedSpaceLimit.mockImplementation(() => {
      return false
    })

    const request = {
      method: 'POST',
      url: '/api/channel',
      body: {
        name: 'my-channel',
        description: 'Some description',
        visibility: false,
        is_private: true,
      },
    }

    await handler(request, res)

    expect(res.json).toHaveBeenCalledWith({
      message: 'Space with that name already exists.',
    })
    expect(res.status).toHaveBeenCalledWith(409)
  })
})

// // import {USER_ROLE_ENUM} from "@memberup/shared/src/types/enum";
// //
// // const userMock = {
// //     membership_id: 1,
// //     role: USER_ROLE_ENUM.member
// // }
// // jest.mock('@/memberup/middlewares/authentication', () => {
// //     return jest.fn()
// // })
//
//
// import mockedMiddleware from '@/memberup/middlewares/mocked-middleware'
// import simpleMiddleware from '@/memberup/middlewares/simple-middleware'
//
// jest.mock('../src/middlewares/simple-middleware')
//
// import handler from '@/memberup/pages/api/feed/simple'
// import nc from "next-connect";
// import {NextApiRequest} from "next";
//
// const myHandler = nc({
//     onError: (err, req, res, next) => {
//         // console.error(err.stack)
//         res.status(500).end('Something broke!')
//     },
//     onNoMatch: (req, res) => {
//         res.status(404).end('Api is not found')
//     }
// })
//
// myHandler.use((req, res, next) => {
//     req['message'] = 'MOCKED'
//     return next()
// })
//
// describe('/api/feed/simple', () => {
//
//     let res
//     beforeEach(() => {
//         res = {
//             status: jest.fn(() => res),
//             end: jest.fn(() => res),
//             json: jest.fn(),
//         }
//     })
//
//     it('GET correct message', async () => {
//
//         simpleMiddleware.mockImplementation(() => {
//             console.log('HEY')
//         })
//
//         const req = {
//             method: 'GET',
//             url: '/api/feed/simple', // NOTE: If we test a handler we must provide the correct url for NextConnect
//             body: {},
//             query: {}
//         }
//         await handler(req, res)
//         expect(res.json).toHaveBeenCalledWith({message: "hello, world"})
//     })
//
//
//     //
//     // it('POST should return 403 if user is not admin or creator', async () => {
//     //     const requestMock = {
//     //         method: 'POST',
//     //         url: '/api/channel', // NOTE: If we test a handler we must provide the correct url for NextConnect
//     //         body: {
//     //             "name": "my-channel",
//     //             "description": "Some channel",
//     //             "visibility": true,
//     //             "is_private": false
//     //         },
//     //         query: {}
//     //     }
//     //
//     //     await handler(requestMock, res)
//     //
//     //     expect(res.json).toHaveBeenCalledWith({
//     //         message: "You don't have the permission.",
//     //     })
//     //     expect(res.status).toHaveBeenCalledWith(403)
//     // })
// })
//
