import { useRouter } from 'next/router'
import MemberItem from '@/memberup/components/member/member-item'
import { selectMembersMap, setActiveMember } from '@/memberup/store/features/memberSlice'
import { selectMembership, selectMembershipTheme } from '@/memberup/store/features/membershipSlice'
import { openDialog } from '@/memberup/store/features/uiSlice'
import { useAppDispatch, useAppSelector } from '@/memberup/store/hooks'
import Box from '@mui/material/Box'
import Grid from '@mui/material/Grid'
import Tab from '@mui/material/Tab'
import Tabs from '@mui/material/Tabs'
import Typography from '@mui/material/Typography'
import { useState } from 'react'
import InfiniteScroll from 'react-infinite-scroll-component'
import { IUser } from '@/shared-types/interfaces'
import { checkAdminOrCreatorRole } from '@/shared-libs/profile'

export default function MembersListing({ status }) {
  const router = useRouter()
  const dispatch = useAppDispatch()
  const membershipTheme = useAppSelector((state) => selectMembershipTheme(state))
  const membership = useAppSelector((state) => selectMembership(state))
  const [activeTab, setActiveTab] = useState(status)
  const membersMap = useAppSelector((state) => selectMembersMap(state))
  const handleClickMember = async (item: IUser) => {
    dispatch(setActiveMember(item))
    dispatch(openDialog({ dialog: 'UserProfile', open: true, props: {} }))
  }

  const handleTabChange = (newTab) => {
    setActiveTab(newTab)
    router.replace(`/members/${newTab}`)
  }

  const allUsers = Object.values(membersMap)
  const admins = allUsers.filter((m: any) =>
    checkAdminOrCreatorRole(m?.role, m?.membership_id, membership?.id)
  )
  const members = allUsers.filter(
    (m: any) => !checkAdminOrCreatorRole(m?.role, m?.membership_id, membership?.id)
  )
  const onlineMembers = members.filter((m: any) => m.online)
  const onlineAdmins = admins.filter((o: any) => o.online)

  let numberOfUsers =
    activeTab === 'online' ? onlineMembers.length + onlineAdmins.length : allUsers.length
  let selectedMembers =
    activeTab === 'online' ? onlineAdmins.concat(onlineMembers) : admins.concat(members)

  return (
    <Box
      className="d-flex direction-column no-wrap"
      sx={{
        py: '16px',
        position: 'relative',
        height: '100%',
        width: '100%',
      }}
    >
      <Box sx={{ display: { xs: 'block', sm: 'none' }, marginBottom: 5 }}>
        <Grid
          sx={{
            marginLeft: { xs: '25px', sm: 'auto' },
            marginBottom: { xs: '-20px', sm: 'auto' },
            justifyContent: { xs: 'center', sm: 'flex-start' },
          }}
          container
          alignItems="center"
        >
          <Grid item xs>
            <Typography variant="h5">Members</Typography>
          </Grid>
        </Grid>
      </Box>
      <Box>
        <Grid
          container
          sx={{
            marginLeft: { xs: '25px', sm: 'auto' },
            justifyContent: { xs: 'center', sm: 'flex-start' },
          }}
        >
          <Grid item xs>
            <Tabs
              value={activeTab}
              indicatorColor="primary"
              textColor="primary"
              onChange={(event, newValue) => handleTabChange(newValue)}
              sx={{
                '& .MuiTab-root ': {
                  fontFamily: 'Graphik Semibold',
                  fontSize: '16px',
                },
              }}
            >
              <Tab value="all" label="All" />
              <Tab value="online" label="Online" />
            </Tabs>
          </Grid>
        </Grid>
      </Box>
      <Box
        id="members-wrapper"
        sx={{
          position: 'relative',
          display: 'flex',
          flexGrow: 1,
          justifyContent: 'center',
          alignItems: 'flex-start',
          width: { xs: '100%', sm: 'calc(100% + 24px)' },
          marginLeft: { xs: '10px', sm: '-12px' },
          marginRight: { xs: '10px', sm: '-12px' },
          overflowX: 'hidden',
          overflowY: 'auto',
        }}
      >
        <InfiniteScroll
          dataLength={numberOfUsers}
          next={() => {}}
          hasMore={false}
          loader={null}
          scrollableTarget="members-wrapper"
          style={{ overflow: 'hidden' }}
        >
          <Box
            sx={{
              display: 'flex',
              flexWrap: 'wrap',
              flexGrow: 1,
              jsutifyContent: 'flex-start',
              alignItems: 'flex-start',
            }}
          >
            {selectedMembers.map((item: any, index) => (
              <Box
                key={item.id}
                sx={{
                  cursor: 'pointer',
                  display:
                    (activeTab === 'online' && item.online) || activeTab === 'all'
                      ? 'block'
                      : 'none',
                  ml: { xs: 'auto', sm: 3 },
                  mr: { xs: 'auto', sm: 3 },
                  mt: { xs: 5, sm: 6 },
                }}
              >
                <MemberItem member={item} handleClick={() => handleClickMember(item)} />
              </Box>
            ))}
            {numberOfUsers === 0 && (
              <div style={{ width: '100%' }}>
                <br />
                <br />
                <br />
                <br />
                <br />
                <Typography className="text-center" color="text.disabled" variant="body1">
                  No Members
                </Typography>
              </div>
            )}
          </Box>
        </InfiniteScroll>
      </Box>
    </Box>
  )
}
