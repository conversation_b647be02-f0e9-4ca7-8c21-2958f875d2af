import { createSlice, PayloadAction } from '@reduxjs/toolkit'
import { RootState } from '../store'
import {ALGOLIA_INDEX_ENUM} from "@/src/libs/algolia";

type DialogPayload = {
  dialog:
    | 'CreatePostReminder'
    | 'EditEvent'
    | 'EditLibraryContent'
    | 'EditPost'
    | 'EditSpace'
    | 'EditStream'
    | 'EditStreamInfo'
    | 'EditUserProfile'
    | 'LiveVideoSetup'
    | 'ManageAccount'
    | 'Notifications'
    | 'Search'
    | 'UserProfile'
    | 'WarningEditingContent'
    | 'CustomizeContentHeader'
    | 'UpgradePlanSpaceLimit'
    | 'MemberProfile'
    | 'MemberSettings'
    | 'AdminSettings'
    | 'SignUp'
    | 'VerifyEmail'
    | 'Login'
    | 'CreateCommunity'
    | 'JoinCommunityCheckout'
    | 'JoinCommunityQuestions'
    | 'MembershipPending'
    | 'CustomizeCommunity'
    | 'CustomizeTheme'

  open: boolean
  props?: any
}
export interface UIState {
  openDialogs: {
    [dialog: string]: {
      open: boolean
      props: any
    }
  }
  visibleTopSectionInHomePage: boolean
  visibleVideoSectionInGettingStart: boolean
  visibleTaskSectionInGettingStart: boolean
  markReadNotification: string
  refresh: boolean
  searchStatus: {
    member: boolean | null
    feed: boolean | null
    'content-library': boolean | null
  }
  optimisticKnockUnseen: boolean
  membershipRequestToCancel: string
}

const initialState: UIState = {
  openDialogs: {},
  visibleTopSectionInHomePage: true,
  visibleVideoSectionInGettingStart: true,
  visibleTaskSectionInGettingStart: true,
  markReadNotification: null,
  refresh: false,
  searchStatus: {
    ALGOLIA_INDEX_ENUM.FEED.: false,
    ALGOLIA_INDEX_ENUM.MEMBER: false,
    ALGOLIA_INDEX_ENUM.CONTENT_LIBRARY: false,
  },
  optimisticKnockUnseen: false,
  membershipRequestToCancel: null,
}

export const uiSlice = createSlice({
  name: 'uiSlice',
  initialState,
  // The `reducers` field lets us define reducers and generate associated actions
  reducers: {
    // Use the PayloadAction type to declare the contents of `action.payload`
    openDialog: (state, action: PayloadAction<DialogPayload>) => {
      if (action.payload.open) {
        state.openDialogs[action.payload.dialog] = {
          open: action.payload.open,
          props: action.payload.props,
        }
      } else {
        delete state.openDialogs[action.payload.dialog]
      }
    },
    resetDialogs: (state) => {
      state.openDialogs = {}
    },
    setMarkReadNotification: (state, action: PayloadAction<string>) => {
      state.markReadNotification = action.payload
    },
    setUIRefresh: (state) => {
      state.refresh = !state.refresh
    },
    setSearchStatus: (state, action: PayloadAction<{ searchIndex: string; hasResults: boolean }>) => {
      state.searchStatus[action.payload.searchIndex] = action.payload.hasResults
    },
    setOptimisticKnockUnseen: (state, action: PayloadAction<boolean>) => {
      state.optimisticKnockUnseen = action.payload
    },
    setMembershipRequestToCancel: (state, action: PayloadAction<string>) => {
      state.membershipRequestToCancel = action.payload
    },
  },
})

export const { openDialog, resetDialogs, setMarkReadNotification, setSearchStatus, setOptimisticKnockUnseen } =
  uiSlice.actions
export const selectOpenDialogs = (state: RootState) => state.ui.openDialogs
export const selectMarkReadNotification = (state: RootState) => state.ui.markReadNotification
export const selectOptimisticKnockUnseen = (state: RootState) => state.ui.optimisticKnockUnseen
export default uiSlice.reducer
