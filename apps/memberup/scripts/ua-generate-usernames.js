require('dotenv').config()

const { PrismaClient } = require('@prisma/client')
const { StreamChat } = require('stream-chat')
const _uniq = require('lodash/uniq')
const { askQuestion, getStreamApps } = require('./common')
const prisma = new PrismaClient()

// NOTE: We don't generate usernames for invited users as they will create their username after completing the signup process.

function generateUsername(firstName, lastName) {
  // Create an array with the non-empty first and last names
  const sanitizeString = (str) => str.replace(/[^a-zA-Z0-9]/g, '')

  // Sanitize the first and last names
  firstName = sanitizeString(firstName)
  lastName = sanitizeString(lastName)
  let parts = [firstName, lastName].filter((name) => name !== '' && name !== null)
  // Join the parts with a dash if both are present
  let baseUsername = parts.join('-')
  // Generate a random 5-character alphanumeric string
  let randomString = Math.random().toString(36).substring(2, 7)
  // Create the final username
  let username = baseUsername ? `${baseUsername}-${randomString}` : randomString
  return username.toLowerCase()
}
const DATABASE_URL = process.env.DATABASE_URL
const GET_STREAM_APP_KEY = process.env.NEXT_PUBLIC_GET_STREAM_APP_KEY
const GET_STREAM_APP_SECRET = process.env.GET_STREAM_APP_SECRET

const delay = (ms) => new Promise((res) => setTimeout(res, ms))

const main = async () => {
  try {
    console.log(`Using Database URL ${DATABASE_URL}`)
    console.log(`Using GetStream App: key=${GET_STREAM_APP_KEY}, name=${getStreamApps[GET_STREAM_APP_KEY].name}`)
    const answer = await askQuestion('Do you want to proceed? (y/n): ')
    if (answer.toLowerCase() !== 'y') {
      console.log('Good bye.')
      return
    }

    const client = new StreamChat(GET_STREAM_APP_KEY, GET_STREAM_APP_SECRET, {
      timeout: 30000,
    })

    const whereClause = {
      status: {
        not: {
          in: ['deleted', 'invited'],
        },
      },
      username: null,
      membership_id: '3a9730ad-cbd5-43b9-bfb3-ca5120c371aa',
    }

    const users = await prisma.user.findMany({
      where: whereClause,
      orderBy: {
        id: 'desc',
      },
    })
    console.log(`Going to update ${users.length} users`)

    const batchSize = 10 // Define the size of each chunk
    const totalBatches = Math.ceil(users.length / batchSize)
    for (let i = 0; i < users.length; i += batchSize) {
      console.log(`Processing batch ${i / batchSize + 1} of ${totalBatches}`)
      const usersBatch = users.slice(i, i + batchSize)
      // Batch update users with new usernames
      const updates = usersBatch.map((user) => {
        const newUsername = generateUsername(user.first_name, user.last_name)
        return prisma.user.update({
          where: {
            id: user.id,
          },
          data: {
            username: newUsername,
          },
        })
      })
      // Execute all updates concurrently
      const results = await Promise.all(updates)
      for (let r of results) {
        console.log('user id =====', r.id)
        const update = {
          id: r.id,
          set: {
            username: r.username,
          },
        }
        // response will contain object {userID => user} with updated users info
        try {
          const response = await client.partialUpdateUser(update)
          await delay(250)
        } catch (e) {
          console.error(e)
        }
      }
    }
  } catch (e) {
    console.error(e)
    process.exit(1)
  } finally {
  }
}

main()
