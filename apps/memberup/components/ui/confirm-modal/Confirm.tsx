import { Button, Input } from '@/components/ui'
import { Modal, ModalContent, ModalDescription, ModalFooter, ModalHeader, ModalTitle } from '@/components/ui/modal'

export function ConfirmModal({
  children,
  onConfirm,
  open,
  onClose,
  title,
  loading = false,
  showCancelButton = true,
  showConfirmButton = true,
}: {
  children?: React.ReactNode
  onConfirm?: () => void
  open: boolean
  onClose: () => void
  title: string
  loading?: boolean
  showCancelButton?: boolean // Show or hide the Cancel button
  showConfirmButton?: boolean // Show or hide the Confirm button
}) {
  return (
    <Modal open={open} onOpenChange={(value) => !value && onClose()}>
      <ModalContent>
        <ModalHeader>
          <ModalTitle>{title}</ModalTitle>
          <ModalDescription>{children}</ModalDescription>
        </ModalHeader>
        <div className="flex flex-col space-y-5"></div>
        <ModalFooter>
          {showCancelButton && (
            <Button disabled={loading} className="w-full sm:w-32" variant="outline" onClick={() => onClose()}>
              Cancel
            </Button>
          )}
          {showConfirmButton && (
            <Button
              className="w-full sm:w-32"
              variant="destructive"
              loading={loading}
              disabled={loading}
              onClick={() => {
                onConfirm?.()
              }}
            >
              Confirm
            </Button>
          )}
        </ModalFooter>
      </ModalContent>
    </Modal>
  )
}
